from collections import defaultdict

def group_anagrams(words):
    anagram_groups = defaultdict(list)
    print(f"Input words: {words}")
    for word in words:
        sorted_word = ''.join(sorted(word))
        print(f"Word: '{word}' -> Sorted: '{sorted_word}'")
        anagram_groups[sorted_word].append(word)
    print(f"Anagram groups: {dict(anagram_groups)}")
    return anagram_groups.values()

def main():
    n = int(input())
    words = [val for val in input().split()]
    
    groups = group_anagrams(words)
    for group in groups:
        sorted_group = sorted(group)
        print("Anagrams:", ' '.join(sorted_group))

if __name__ == "__main__":
    main()
