from collections import defaultdict

def group_anagrams(words):
    anagram_groups = defaultdict(list)
    for word in words:
        sorted_word = ''.join(sorted(word))
        anagram_groups[sorted_word].append(sorted_word)
    return anagram_groups.values()

def main():
    import sys
    input_lines = sys.stdin.read().splitlines()
    n = int(input_lines[0])
    words = input_lines[1:n+1]
    
    groups = group_anagrams(words)
    for group in groups:
        print("Anagrams:", ' '.join(group))

if __name__ == "__main__":
    main()